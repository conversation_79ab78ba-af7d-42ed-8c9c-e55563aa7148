<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Find aftermarket automotive parts including nuts, screws, bolts, engine spare parts, bushes, and starters.">
    <title>Aftermarket Automotive Parts | ShopEasy</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        /* Global Styles */
        :root {
            /* Base Colors - Light Theme */
            --primary-color: #2EC0CB ;
            --primary-dark: #333333;
            --secondary-color: #f5f5f5;
            --accent-color: #666666;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #ffffff;
            --dark-color: #212529;
            --gray-color: #6c757d;
            --gray-light: #e9ecef;
            --gray-dark: #343a40;

            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-xxl: 3rem;

            /* Typography */
            --font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-md: 1rem;
            --font-size-lg: 1.25rem;
            --font-size-xl: 1.5rem;
            --font-size-xxl: 2rem;

            /* Border Radius */
            --border-radius-sm: 0.25rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 1rem;

            /* Shadows */
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);

            /* Transitions */
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
        }

        /* Dark Theme */
        [data-theme="dark"] {
            --primary-color: #ffffff;
            --secondary-color: #2a2a2a;
            --light-color: #1a1a1a;
            --dark-color: #f8f9fa;
            --gray-color: #adb5bd;
            --gray-light: #343a40;
            --gray-dark: #e9ecef;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--light-color);
            color: var(--dark-color);
            line-height: 1.6;
        }

        .container {
            width: 100%;
            max-width: 100%;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }

        /* Header Styles */
        header {
            background-color: var(--light-color);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) 0;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: var(--font-size-lg);
            font-weight: bold;
            color: var(--dark-color);
            text-decoration: none;
        }

        .logo img {
            margin-right: var(--spacing-sm);
        }

        .main-nav ul {
            display: flex;
            list-style: none;
        }

        .main-nav li {
            margin-left: var(--spacing-md);
        }

        .main-nav a {
            color: var(--dark-color);
            text-decoration: none;
            font-weight: 500;
            transition: color var(--transition-fast);
        }

        .main-nav a:hover, .main-nav a.active {
            color: var(--primary-color);
        }

        .user-actions {
            display: flex;
            align-items: center;
        }

        .search-box {
            position: relative;
            margin-right: var(--spacing-md);
        }

        .search-box input {
            padding: var(--spacing-sm) var(--spacing-md);
            padding-right: 2.5rem;
            border: 1px solid var(--gray-light);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-sm);
            width: 200px;
            transition: all var(--transition-normal);
        }

        .search-box button {
            position: absolute;
            top: 0;
            height: 100%;
            background: none;
            border: none;
            color: var(--gray-color);
            cursor: pointer;
            width: 2.5rem;
        }

        .search-box #search-button {
            right: 0;
        }

        .search-box #clear-search {
            right: 2.5rem;
            display: none; /* Hidden by default, shown when input has text */
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--dark-color);
            cursor: pointer;
            font-size: var(--font-size-md);
            margin-right: var(--spacing-md);
        }

        /* Hero Section */
        .hero {
            background-color: var(--primary-color);
            color: white;
            padding: var(--spacing-xxl) 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: var(--spacing-md);
        }

        .hero p {
            font-size: var(--font-size-lg);
            max-width: 800px;
            margin: 0 auto var(--spacing-xl);
        }

        /* Category Section */
        .categories {
            padding: var(--spacing-xxl) 0;
        }

        .section-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .section-header h2 {
            font-size: var(--font-size-xxl);
            margin-bottom: var(--spacing-sm);
        }

        .section-header p {
            color: var(--gray-color);
            max-width: 600px;
            margin: 0 auto;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: var(--spacing-lg);
        }

        .category-card {
            background-color: var(--light-color);
            border-radius: var(--border-radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: transform var(--transition-normal);
        }

        .category-card:hover {
            transform: translateY(-5px);
        }

        .category-image {
            height: 150px;
            overflow: hidden;
        }

        .category-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform var(--transition-normal);
        }

        .category-card:hover .category-image img {
            transform: scale(1.05);
        }

        .category-info {
            padding: var(--spacing-md);
            text-align: center;
        }

        .category-info h3 {
            margin-bottom: var(--spacing-xs);
        }

        .category-info p {
            color: var(--gray-color);
            font-size: var(--font-size-sm);
        }

        /* Products Section */
        .products {
            padding: var(--spacing-xxl) 0;
            background-color: var(--secondary-color);
        }

        .products-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: var(--spacing-xl);
        }

        .filters {
            background-color: var(--light-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
        }

        .filter-group {
            margin-bottom: var(--spacing-lg);
        }

        .filter-group h3 {
            margin-bottom: var(--spacing-sm);
            font-size: var(--font-size-md);
        }

        .filter-options {
            display: flex;
            flex-direction: column;
        }

        .filter-option {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-xs);
        }

        .filter-option input {
            margin-right: var(--spacing-sm);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-lg);
            justify-content: center;
            margin: 0 auto;
        }

        .product-card {
            background-color: var(--light-color);
            border-radius: var(--border-radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: transform var(--transition-normal);
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform var(--transition-normal);
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        .product-badge {
            position: absolute;
            top: var(--spacing-sm);
            left: var(--spacing-sm);
        }

        .product-badge span {
            display: inline-block;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-xs);
            font-weight: bold;
            text-transform: uppercase;
        }

        .product-badge .new {
            background-color: var(--primary-color);
            color: white;
        }

        .product-badge .sale {
            background-color: var(--accent-color);
            color: white;
        }

        .product-info {
            padding: var(--spacing-md);
        }

        .product-category {
            color: var(--gray-color);
            font-size: var(--font-size-xs);
            margin-bottom: var(--spacing-xs);
        }

        .product-name {
            margin-bottom: var(--spacing-xs);
            font-size: var(--font-size-md);
        }

        .product-price {
            font-weight: bold;
            font-size: var(--font-size-lg);
            margin-bottom: var(--spacing-xs);
            color: var(--primary-color);
        }

        .product-availability {
            font-size: var(--font-size-sm);
            margin-bottom: var(--spacing-sm);
        }

        .in-stock {
            color: var(--success-color);
        }

        .low-stock {
            color: var(--warning-color);
        }

        .out-of-stock {
            color: var(--danger-color);
        }

        .product-actions {
            display: flex;
            justify-content: space-between;
        }

        .btn {
            display: inline-block;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-fast);
            border: none;
            font-size: var(--font-size-sm);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: var(--dark-color);
        }

        .btn-secondary:hover {
            background-color: var(--gray-light);
        }

        /* Footer */
        footer {
            background-color: var(--dark-color);
            color: var(--light-color);
            padding: var(--spacing-xxl) 0;
        }

        .footer-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-xl);
        }

        .footer-column h3 {
            margin-bottom: var(--spacing-md);
            font-size: var(--font-size-md);
        }

        .footer-column ul {
            list-style: none;
        }

        .footer-column li {
            margin-bottom: var(--spacing-sm);
        }

        .footer-column a {
            color: var(--gray-color);
            text-decoration: none;
            transition: color var(--transition-fast);
        }

        .footer-column a:hover {
            color: var(--light-color);
        }

        .copyright {
            text-align: center;
            margin-top: var(--spacing-xxl);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--gray-dark);
            color: var(--gray-color);
            font-size: var(--font-size-sm);
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .products-container {
                grid-template-columns: 1fr;
            }

            .filters {
                margin-bottom: var(--spacing-lg);
            }
        }

        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                padding: var(--spacing-md);
            }

            .logo {
                margin-bottom: var(--spacing-md);
            }

            .main-nav {
                margin-bottom: var(--spacing-md);
            }

            .main-nav ul {
                flex-wrap: wrap;
                justify-content: center;
            }

            .main-nav li {
                margin: 0 var(--spacing-sm) var(--spacing-sm);
            }

            .user-actions {
                width: 100%;
                justify-content: center;
            }

            .search-box {
                width: 100%;
                max-width: 300px;
            }

            .search-box input {
                width: 100%;
            }
        }

        /* Store Finder Styles */
        .store-finder {
            padding: var(--spacing-xxl) 0;
            background-color: var(--light-color);
        }

        .finder-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: var(--spacing-xl);
        }

        .search-filters {
            background-color: var(--light-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 100px;
            max-height: calc(100vh - 120px);
            overflow-y: auto;
            margin-right: var(--spacing-xl);
            border-right: 1px solid var(--gray-light);
        }

        .filter-group {
            margin-bottom: var(--spacing-lg);
            border-bottom: 1px solid var(--gray-light);
            padding-bottom: var(--spacing-md);
        }

        .filter-group:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .filter-group h3 {
            margin-bottom: var(--spacing-sm);
            font-size: var(--font-size-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .filter-group h3 i {
            color: var(--primary-color);
            font-size: 0.9em;
        }

        .range-slider {
            width: 100%;
            margin: var(--spacing-sm) 0;
            -webkit-appearance: none;
            appearance: none;
            height: 8px;
            border-radius: 4px;
            background: var(--gray-light);
            outline: none;
        }

        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .range-slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .range-values {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-xs);
            color: var(--gray-color);
        }

        .filter-select {
            width: 100%;
            padding: var(--spacing-sm);
            border: 1px solid var(--gray-light);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-sm);
            color: var(--dark-color);
            background-color: var(--light-color);
        }

        .filter-tabs {
            display: flex;
            margin-bottom: var(--spacing-sm);
        }

        .filter-tab {
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid var(--gray-light);
            background: var(--light-color);
            font-size: var(--font-size-xs);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .filter-tab:first-child {
            border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
        }

        .filter-tab:last-child {
            border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
        }

        .filter-tab.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .filter-view {
            display: none;
        }

        .filter-view.active {
            display: block;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            max-height: 200px;
            overflow-y: auto;
            padding-right: var(--spacing-sm);
        }

        .filter-checkbox {
            display: flex;
            align-items: center;
            font-size: var(--font-size-sm);
            cursor: pointer;
        }

        .filter-checkbox input {
            margin-right: var(--spacing-sm);
        }

        .btn-icon {
            background: none;
            border: none;
            color: var(--gray-color);
            cursor: pointer;
            font-size: var(--font-size-md);
            padding: var(--spacing-xs);
            transition: color var(--transition-fast);
        }

        .btn-icon:hover {
            color: var(--primary-color);
        }

        .store-listings-container {
            width: 100%;
        }

        .store-listings .section-header {
            margin-bottom: var(--spacing-lg);
            text-align: left;
        }

        .store-listings .section-header h3 {
            font-size: var(--font-size-xl);
            margin-bottom: var(--spacing-xs);
        }

        .store-listings .section-header p {
            color: var(--gray-color);
        }

        .stores-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        /* Express Store Badge */
        .express {
            background: #4a6fa5;
            color: white;
        }

        [data-theme="dark"] .express {
            background: #6a8ec5;
            color: black;
        }

        .store-card {
            background-color: var(--light-color);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: all var(--transition-normal);
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .store-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
            border-color: var(--primary-color);
        }

        .store-status {
            position: absolute;
            top: var(--spacing-sm);
            left: var(--spacing-sm);
            z-index: 5;
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            backdrop-filter: blur(5px);
        }

        .store-status.open {
            background-color: rgba(40, 167, 69, 0.2);
            color: var(--success-color);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .store-status.closed {
            background-color: rgba(220, 53, 69, 0.2);
            color: var(--danger-color);
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .store-status i {
            font-size: 8px;
        }

        .store-image {
            position: relative;
            height: 180px;
            overflow: hidden;
        }

        .store-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .store-card:hover .store-image img {
            transform: scale(1.05);
        }

        .store-favorite {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            background-color: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 3;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .store-favorite:hover {
            background-color: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }

        .store-favorite.active i {
            color: #e74c3c;
        }

        .store-badge {
            position: absolute;
            bottom: var(--spacing-sm);
            left: var(--spacing-sm);
            z-index: 2;
        }

        .store-badge span {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .flagship {
            background: #000000;
            color: white;
        }

        .outlet {
            background: #333333;
            color: white;
        }

        .partner {
            background: #555555;
            color: white;
        }

        [data-theme="dark"] .flagship {
            background: #ffffff;
            color: black;
        }

        [data-theme="dark"] .outlet {
            background: #dddddd;
            color: black;
        }

        [data-theme="dark"] .partner {
            background: #bbbbbb;
            color: black;
        }

        .store-info {
            padding: var(--spacing-md);
        }

        .store-info h3 {
            font-size: var(--font-size-lg);
            margin-bottom: var(--spacing-xs);
        }

        .store-address, .store-distance {
            font-size: var(--font-size-sm);
            color: var(--gray-color);
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .store-rating {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            font-size: var(--font-size-sm);
        }

        .stars {
            color: #ffc107;
        }

        .store-services {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-md);
        }

        .service-tag {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: var(--secondary-color);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-xs);
            color: var(--dark-color);
        }

        .store-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        /* Recommendations Section */
        .recommendations {
            padding: var(--spacing-xxl) 0;
            background-color: var(--secondary-color);
            position: relative;
            width: 100%;
        }

        .recommendations .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .recommendation-tabs {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
            width: 100%;
        }

        .tab-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            background-color: var(--light-color);
            color: var(--dark-color);
            border-radius: var(--border-radius-md);
            cursor: pointer;
            transition: background-color var(--transition-normal), color var(--transition-normal);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .tab-btn.active {
            background-color: var(--primary-color);
            color: white;
        }

        .wishlist-btn {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            background-color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: transform var(--transition-normal);
            z-index: 2;
        }

        .wishlist-btn:hover {
            transform: scale(1.1);
        }

        .btn-sm {
            padding: var(--spacing-xs) var(--spacing-md);
            font-size: var(--font-size-sm);
        }

        /* Delivery Options */
        .product-delivery-options {
            margin-bottom: var(--spacing-sm);
        }

        .delivery-option-select,
        .time-slot-select {
            width: 100%;
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid var(--gray-light);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-sm);
            margin-bottom: var(--spacing-xs);
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        .time-slot-select {
            margin-top: var(--spacing-xs);
        }

        /* Store Products View */
        .store-products-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl) var(--spacing-md);
            background-color: var(--light-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-xl);
        }

        .store-products-header {
            margin-bottom: var(--spacing-xl);
            text-align: center;
            padding-bottom: var(--spacing-lg);
            border-bottom: 2px solid var(--gray-light);
        }

        .store-products-header h2 {
            font-size: var(--font-size-xxl);
            margin-bottom: var(--spacing-sm);
            color: var(--primary-color);
        }

        .store-products-header p {
            color: var(--gray-color);
            margin-bottom: var(--spacing-lg);
            font-size: var(--font-size-lg);
        }

        .back-to-recommendations {
            margin-top: var(--spacing-md);
            display: inline-block;
        }

        .store-products-container .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            justify-items: center;
            align-items: start;
        }

        .store-products-container .product-card {
            width: 100%;
            max-width: 320px;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .products-container {
                grid-template-columns: 1fr;
            }

            .filters {
                margin-bottom: var(--spacing-lg);
            }

            .finder-container {
                grid-template-columns: 1fr;
            }

            .search-filters {
                position: static;
                max-height: none;
                margin-right: 0;
                border-right: none;
                margin-bottom: var(--spacing-lg);
            }

            .products-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                padding: var(--spacing-md);
            }

            .logo {
                margin-bottom: var(--spacing-md);
            }

            .main-nav {
                margin-bottom: var(--spacing-md);
            }

            .main-nav ul {
                flex-wrap: wrap;
                justify-content: center;
            }

            .main-nav li {
                margin: 0 var(--spacing-sm) var(--spacing-sm);
            }

            .user-actions {
                width: 100%;
                justify-content: center;
            }

            .search-box {
                width: 100%;
                max-width: 300px;
            }

            .search-box input {
                width: 100%;
            }

            .products-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .stores-grid {
                grid-template-columns: repeat(1, 1fr);
            }

            .store-products-container .products-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--spacing-md);
            }
        }

        @media (min-width: 769px) and (max-width: 992px) {
            .stores-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 993px) and (max-width: 1200px) {
            .stores-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 576px) {
            .category-grid,
            .products-grid,
            .stores-grid {
                grid-template-columns: 1fr;
            }

            .store-products-container .products-grid {
                grid-template-columns: 1fr;
            }

            .store-products-container {
                padding: var(--spacing-md);
            }
        }

        /* Wishlist Popup Styles */
        .wishlist-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .wishlist-popup.show {
            display: flex;
        }

        .wishlist-popup-content {
            background-color: var(--light-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            max-width: 400px;
            width: 90%;
            box-shadow: var(--shadow-lg);
            position: relative;
        }

        .wishlist-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }

        .wishlist-popup-header h3 {
            margin: 0;
            color: var(--dark-color);
        }

        .wishlist-popup-close {
            background: none;
            border: none;
            font-size: var(--font-size-lg);
            color: var(--gray-color);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: 50%;
            transition: all var(--transition-fast);
        }

        .wishlist-popup-close:hover {
            background-color: var(--gray-light);
            color: var(--dark-color);
        }

        .wishlist-groups {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }

        .wishlist-group-option {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm);
            border: 2px solid var(--gray-light);
            border-radius: var(--border-radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .wishlist-group-option:hover {
            border-color: var(--primary-color);
            background-color: rgba(46, 192, 203, 0.1);
        }

        .wishlist-group-option.selected {
            border-color: var(--primary-color);
            background-color: rgba(46, 192, 203, 0.2);
        }

        .wishlist-group-option input[type="radio"] {
            margin-right: var(--spacing-sm);
        }

        .wishlist-group-info {
            flex: 1;
        }

        .wishlist-group-name {
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
        }

        .wishlist-group-description {
            font-size: var(--font-size-sm);
            color: var(--gray-color);
        }

        .wishlist-popup-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
        }

        .new-group-input {
            width: 100%;
            padding: var(--spacing-sm);
            border: 1px solid var(--gray-light);
            border-radius: var(--border-radius-sm);
            margin-top: var(--spacing-xs);
            font-size: var(--font-size-sm);
        }

        .new-group-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-container">
                <h2>Stores and Recommendations</h2>
                <!-- <a href="#" class="logo">
                    <img src="https://via.placeholder.com/40" alt="ShopEasy logo">
                    ShopEasy Auto Parts
                </a>
                <nav class="main-nav">
                    <ul>
                        <li><a href="#">Home</a></li>
                        <li><a href="#">Shop</a></li>
                        <li><a href="#" class="active">Aftermarket Parts</a></li>
                        <li><a href="#">Brands</a></li>
                        <li><a href="#">Deals</a></li>
                        <li><a href="#">Find a Store</a></li>
                    </ul>
                </nav> -->
                <div class="user-actions">
                    <div class="search-box">
                        <input type="text" placeholder="Search parts, brands..." id="header-search">
                        <button aria-label="Search"><i class="fas fa-search"></i></button>
                    </div>
                    <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                        <i class="fas fa-sun light-icon"></i>
                        <i class="fas fa-moon dark-icon" style="display: none;"></i>
                    </button>
                    <a href="#" class="btn btn-secondary"><i class="fas fa-shopping-cart"></i> Cart</a>
                </div>
            </div>
        </div>
    </header>

    <section class="store-finder">
        <div class="container">
            <!-- <div class="section-header">
                <h2>Store Finder</h2>
                <p>Find stores near you, check product availability, and choose between pickup and delivery options.</p>
            </div> -->

            <div class="finder-container">
                <div class="search-filters">
                    <div class="filter-group">
                        <h3>Search Stores & Products</h3>
                        <div class="search-box">
                            <input type="text" placeholder="stores or products......" id="search-input">
                            <button id="clear-search" aria-label="Clear search"><i class="fas fa-times"></i></button>
                            <button id="search-button" aria-label="Search"><i class="fas fa-search"></i></button>
                        </div>
                    </div>

                    <div class="filter-group">
                        <h3>Distance</h3>
                        <input type="range" min="1" max="50" value="10" class="range-slider" id="distance-slider" aria-label="Distance filter slider">
                        <div class="range-values">
                            <span>1 mile</span>
                            <span id="distance-value">10 miles</span>
                            <span>50 miles</span>
                        </div>
                    </div>

                    <div class="filter-group">
                        <h3><i class="fas fa-store"></i> Store Type</h3>

                        <div class="filter-view dropdown-view">
                            <select class="filter-select" id="store-type-dropdown" multiple="multiple" size="3">
                                <option value="Flagship Store" selected>Flagship Stores</option>
                                <option value="Factory Outlet" selected>Factory Outlets</option>
                                <option value="Partner Retailer" selected>Partner Retailers</option>
                                <option value="Express Store" selected>Express Stores</option>
                                <option value="Concept Store" selected>Concept Stores</option>
                                <option value="Pop-up Shop" selected>Pop-up Shops</option>
                            </select>
                        </div>

                        <div class="filter-view checkbox-view active">
                            <div class="checkbox-group">
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Flagship Store" data-filter="store-type" checked>
                                    <span class="checkbox-label">Flagship Stores</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Factory Outlet" data-filter="store-type" checked>
                                    <span class="checkbox-label">Factory Outlets</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Partner Retailer" data-filter="store-type" checked>
                                    <span class="checkbox-label">Partner Retailers</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Express Store" data-filter="store-type" checked>
                                    <span class="checkbox-label">Express Stores</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Concept Store" data-filter="store-type" checked>
                                    <span class="checkbox-label">Concept Stores</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Pop-up Shop" data-filter="store-type" checked>
                                    <span class="checkbox-label">Pop-up Shops</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- <div class="filter-group">
                        <h3><i class="fas fa-tags"></i> Product Categories</h3>

                        <div class="filter-view dropdown-view">
                            <select class="filter-select" id="category-dropdown" multiple="multiple" size="5">
                                <option value="Nuts & Bolts" selected>Nuts & Bolts</option>
                                <option value="Screws" selected>Screws</option>
                                <option value="Engine Parts" selected>Engine Parts</option>
                                <option value="Bushes" selected>Bushes</option>
                                <option value="Starters" selected>Starters</option>
                                <option value="Brake System">Brake System</option>
                                <option value="Suspension">Suspension</option>
                                <option value="Electrical">Electrical</option>
                                <option value="Filters">Filters</option>
                                <option value="Accessories">Accessories</option>
                            </select>
                        </div>

                        <div class="filter-view checkbox-view active">
                            <div class="checkbox-group">
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Nuts & Bolts" data-filter="category" checked>
                                    <span class="checkbox-label">Nuts & Bolts</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Screws" data-filter="category" checked>
                                    <span class="checkbox-label">Screws</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Engine Parts" data-filter="category" checked>
                                    <span class="checkbox-label">Engine Parts</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Bushes" data-filter="category" checked>
                                    <span class="checkbox-label">Bushes</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Starters" data-filter="category" checked>
                                    <span class="checkbox-label">Starters</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Brake System" data-filter="category">
                                    <span class="checkbox-label">Brake System</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Suspension" data-filter="category">
                                    <span class="checkbox-label">Suspension</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Electrical" data-filter="category">
                                    <span class="checkbox-label">Electrical</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Filters" data-filter="category">
                                    <span class="checkbox-label">Filters</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Accessories" data-filter="category">
                                    <span class="checkbox-label">Accessories</span>
                                </label>
                            </div>
                        </div>
                    </div> -->

                    <div class="filter-group">
                        <h3><i class="fas fa-concierge-bell"></i> Services</h3>


                        <div class="filter-view dropdown-view">
                            <select class="filter-select" id="service-dropdown" multiple="multiple" size="4">
                                <option value="In-Store Pickup" selected>In-Store Pickup</option>
                                <option value="Same-Day Delivery" selected>Same-Day Delivery</option>
                                <option value="Curbside Pickup" selected>Curbside Pickup</option>
                                <option value="Repair Services">Repair Services</option>
                                <option value="Installation">Installation</option>
                                <option value="Technical Support">Technical Support</option>
                            </select>
                        </div>

                        <div class="filter-view checkbox-view active">
                            <div class="checkbox-group">
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="In-Store Pickup" data-filter="service" checked>
                                    <span class="checkbox-label">In-Store Pickup</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Same-Day Delivery" data-filter="service" checked>
                                    <span class="checkbox-label">Same-Day Delivery</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Curbside Pickup" data-filter="service" checked>
                                    <span class="checkbox-label">Curbside Pickup</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Repair Services" data-filter="service">
                                    <span class="checkbox-label">Repair Services</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Installation" data-filter="service">
                                    <span class="checkbox-label">Installation</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" value="Technical Support" data-filter="service">
                                    <span class="checkbox-label">Technical Support</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- <div class="filter-group">
                        <h3><i class="fas fa-sort-amount-down"></i> Sort By</h3>
                        <select class="filter-select" id="sort-filter">
                            <option value="distance">Distance (Nearest First)</option>
                            <option value="rating">Rating (Highest First)</option>
                            <option value="popularity">Popularity</option>
                            <option value="name-asc">Name (A-Z)</option>
                            <option value="name-desc">Name (Z-A)</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <h3><i class="fas fa-store"></i> Sort by Store Type</h3>
                        <select class="filter-select" id="store-type-sort">
                            <option value="all">All Store Types</option>
                            <option value="Flagship Store">Flagship Stores</option>
                            <option value="Factory Outlet">Factory Outlets</option>
                            <option value="Partner Retailer">Partner Retailers</option>
                            <option value="Express Store">Express Stores</option>
                            <option value="Concept Store">Concept Stores</option>
                            <option value="Pop-up Shop">Pop-up Shops</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <h3><i class="fas fa-tags"></i> Sort by Product Category</h3>
                        <select class="filter-select" id="category-sort">
                            <option value="all">All Categories</option>
                            <option value="Nuts & Bolts">Nuts & Bolts</option>
                            <option value="Screws">Screws</option>
                            <option value="Engine Parts">Engine Parts</option>
                            <option value="Bushes">Bushes</option>
                            <option value="Starters">Starters</option>
                            <option value="Brake System">Brake System</option>
                            <option value="Suspension">Suspension</option>
                            <option value="Electrical">Electrical</option>
                            <option value="Filters">Filters</option>
                            <option value="Accessories">Accessories</option>
                        </select>
                    </div> -->

                    <div class="filter-group">
                        <h3><i class="fas fa-concierge-bell"></i> Sort by Service</h3>
                        <select class="filter-select" id="service-sort">
                            <option value="all">All Services</option>
                            <option value="In-Store Pickup">In-Store Pickup</option>
                            <option value="Same-Day Delivery">Same-Day Delivery</option>
                            <option value="Curbside Pickup">Curbside Pickup</option>
                            <option value="Repair Services">Repair Services</option>
                            <option value="Installation">Installation</option>
                            <option value="Technical Support">Technical Support</option>
                            <option value="Warranty Service">Warranty Service</option>
                            <option value="Special Orders">Special Orders</option>
                        </select>
                    </div>

                    <button class="btn btn-primary" id="apply-filters">Apply Filters</button>
                </div>

                <div class="store-listings-container">
                    <div class="store-listings">
                        <div class="section-header">
                            <h3>Nearby Stores</h3>
                            <p id="store-count">We found 4 stores within 10 miles of your location</p>
                        </div>

                        <div class="stores-grid">
                            <div class="store-card" id="store-downtown">
                                <div class="store-image">
                                    <img src="images/Bosch.jpg" alt="Bosch">
                                    <div class="store-badge">
                                        <span class="flagship">Flagship Store</span>
                                    </div>
                                    <div class="store-status open">
                                        <i class="fas fa-circle"></i> Open Now
                                    </div>
                                    <button class="store-favorite" aria-label="Add to favorites"><i class="far fa-heart"></i></button>
                                </div>
                                <div class="store-info">
                                    <h3>Bosch Store</h3>
                                    <p class="store-address"><i class="fas fa-map-marker-alt"></i> 123 Main St, Mumbai, MH 400001</p>
                                    <p class="store-distance"><i class="fas fa-route"></i> 0.8 miles away</p>
                                    <div class="store-rating">
                                        <span class="stars">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star-half-alt"></i>
                                        </span>
                                        <span>4.5 (120)</span>
                                    </div>
                                    <div class="store-services">
                                        <span class="service-tag"><i class="fas fa-shopping-bag"></i> In-Store Pickup</span>
                                        <span class="service-tag"><i class="fas fa-truck"></i> Same-Day Delivery</span>
                                        <span class="service-tag"><i class="fas fa-car"></i> Curbside Pickup</span>
                                    </div>
                                    <div class="store-actions">
                                        <button class="btn btn-sm btn-primary view-store" data-store="downtown">View Store</button>
                                        <button class="btn btn-sm btn-secondary contact-store">Contact Store</button>
                                    </div>
                                </div>
                            </div>

                            <div class="store-card" id="store-outlet">
                                <div class="store-image">
                                    <img src="images/tata.jpg" alt="Tata Auto Parts Outlet">
                                    <div class="store-badge">
                                        <span class="outlet">Factory Outlet</span>
                                    </div>
                                    <div class="store-status open">
                                        <i class="fas fa-circle"></i> Open Now
                                    </div>
                                    <button class="store-favorite" aria-label="Add to favorites"><i class="far fa-heart"></i></button>
                                </div>
                                <div class="store-info">
                                    <h3>Tata Auto Parts Outlet</h3>
                                    <p class="store-address"><i class="fas fa-map-marker-alt"></i> 500 Outlet Blvd, Delhi, DL 110001</p>
                                    <p class="store-distance"><i class="fas fa-route"></i> 3.2 miles away</p>
                                    <div class="store-rating">
                                        <span class="stars">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </span>
                                        <span>4.0 (85)</span>
                                    </div>
                                    <div class="store-services">
                                        <span class="service-tag"><i class="fas fa-shopping-bag"></i> In-Store Pickup</span>
                                        <span class="service-tag"><i class="fas fa-percent"></i> Clearance Items</span>
                                    </div>
                                    <div class="store-actions">
                                        <button class="btn btn-sm btn-primary view-store" data-store="outlet">View Store</button>
                                        <button class="btn btn-sm btn-secondary contact-store">Contact Store</button>
                                    </div>
                                </div>
                            </div>

                            <div class="store-card" id="store-partner">
                                <div class="store-image">
                                    <img src="images/hyn.jpg" alt="hyn">
                                    <div class="store-badge">
                                        <span class="partner">Partner Retailer</span>
                                    </div>
                                    <div class="store-status closed">
                                        <i class="fas fa-circle"></i> Closed
                                    </div>
                                    <button class="store-favorite" aria-label="Add to favorites"><i class="far fa-heart"></i></button>
                                </div>
                                <div class="store-info">
                                    <h3>Hyundai Partner Store</h3>
                                    <p class="store-address"><i class="fas fa-map-marker-alt"></i> 789 Tech Ave, Bangalore, KA 560001</p>
                                    <p class="store-distance"><i class="fas fa-route"></i> 4.5 miles away</p>
                                    <div class="store-rating">
                                        <span class="stars">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </span>
                                        <span>5.0 (42)</span>
                                    </div>
                                    <div class="store-services">
                                        <span class="service-tag"><i class="fas fa-truck"></i> Same-Day Delivery</span>
                                        <span class="service-tag"><i class="fas fa-tools"></i> Repair Services</span>
                                    </div>
                                    <div class="store-actions">
                                        <button class="btn btn-sm btn-primary view-store" data-store="partner">View Store</button>
                                        <button class="btn btn-sm btn-secondary contact-store">Contact Store</button>
                                    </div>
                                </div>
                            </div>

                            <div class="store-card" id="store-express">
                                <div class="store-image">
                                    <img src="images/mahindra.jpg" alt="ShopEasy Express">
                                    <div class="store-badge">
                                        <span class="express">Express Store</span>
                                    </div>
                                    <div class="store-status open">
                                        <i class="fas fa-circle"></i> Open Now
                                    </div>
                                    <button class="store-favorite" aria-label="Add to favorites"><i class="far fa-heart"></i></button>
                                </div>
                                <div class="store-info">
                                    <h3>ShopEasy Express</h3>
                                    <p class="store-address"><i class="fas fa-map-marker-alt"></i> 456 Express Way, Chennai, TN 600001</p>
                                    <p class="store-distance"><i class="fas fa-route"></i> 5.7 miles away</p>
                                    <div class="store-rating">
                                        <span class="stars">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </span>
                                        <span>4.0 (56)</span>
                                    </div>
                                    <div class="store-services">
                                        <span class="service-tag"><i class="fas fa-shopping-bag"></i> In-Store Pickup</span>
                                        <span class="service-tag"><i class="fas fa-truck"></i> Same-Day Delivery</span>
                                    </div>
                                    <div class="store-actions">
                                        <button class="btn btn-sm btn-primary view-store" data-store="express">View Store</button>
                                        <button class="btn btn-sm btn-secondary contact-store">Contact Store</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="recommendations">
        <div class="container">
            <div class="section-header">
                <h2>Recommended For You</h2>
                <p>Based on your preferences and nearby store availability</p>
            </div>

            <div class="recommendation-tabs">
                <button class="tab-btn active" data-tab="trending">Trending Nearby</button>
                <button class="tab-btn" data-tab="recently-viewed">Recently Viewed</button>
                <button class="tab-btn" data-tab="new-arrivals">Just Arrived</button>
                <button class="tab-btn" data-tab="on-sale">On Sale</button>
            </div>

            <div class="products-grid" id="recommendation-products">
                <div class="product-card" data-tab="trending new-arrivals">
                    <div class="product-image">
                        <img src="images/brake-pads.jpg" alt="Premium Brake Pads">
                        <div class="product-badge">
                            <span class="new">New Arrival</span>
                        </div>
                        <button class="wishlist-btn" data-product="Premium Brake Pads"><i class="far fa-heart"></i></button>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Brake System</div>
                        <h3 class="product-name">Premium Brake Pads (Front)</h3>
                        <div class="product-price">₹1,299</div>
                        <div class="product-availability">
                            <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock at 3 Nearby Stores</span>
                        </div>
                        <div class="product-delivery-options">
                            <select class="delivery-option-select">
                                <option value="">Select Pickup/Delivery Option</option>
                                <option value="pickup">Pickup In-Store</option>
                                <option value="delivery">Home Delivery</option>
                            </select>
                            <select class="time-slot-select" style="display: none;">
                                <option value="">Select Pickup Time</option>
                                <option value="10-11">10:00 AM - 11:00 AM</option>
                                <option value="11-12">11:00 AM - 12:00 PM</option>
                                <option value="12-1">12:00 PM - 1:00 PM</option>
                                <option value="1-2">1:00 PM - 2:00 PM</option>
                                <option value="2-3">2:00 PM - 3:00 PM</option>
                                <option value="3-4">3:00 PM - 4:00 PM</option>
                                <option value="4-5">4:00 PM - 5:00 PM</option>
                                <option value="5-6">5:00 PM - 6:00 PM</option>
                            </select>
                        </div>
                        <div class="product-actions">
                            <button class="btn btn-sm btn-primary add-to-cart" data-product="Premium Brake Pads">Add to Cart</button>
                            <button class="btn btn-sm btn-secondary find-in-store" data-product="Premium Brake Pads">Find in Store</button>
                        </div>
                    </div>
                </div>

                <div class="product-card" data-tab="trending on-sale">
                    <div class="product-image">
                        <img src="images/oil-filter-kit.jpg" alt="Oil Filter Kit">
                        <div class="product-badge">
                            <span class="sale">25% OFF</span>
                        </div>
                        <button class="wishlist-btn" data-product="Oil Filter Kit"><i class="far fa-heart"></i></button>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Engine Maintenance</div>
                        <h3 class="product-name">Oil Filter Kit with Gasket</h3>
                        <div class="product-price">₹599 <span style="text-decoration: line-through; font-size: 0.8em; color: var(--gray-color);">₹799</span></div>
                        <div class="product-availability">
                            <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock at 5 Nearby Stores</span>
                        </div>
                        <div class="product-delivery-options">
                            <select class="delivery-option-select">
                                <option value="">Select Pickup/Delivery Option</option>
                                <option value="pickup">Pickup In-Store</option>
                                <option value="delivery">Home Delivery</option>
                            </select>
                            <select class="time-slot-select" style="display: none;">
                                <option value="">Select Pickup Time</option>
                                <option value="10-11">10:00 AM - 11:00 AM</option>
                                <option value="11-12">11:00 AM - 12:00 PM</option>
                                <option value="12-1">12:00 PM - 1:00 PM</option>
                                <option value="1-2">1:00 PM - 2:00 PM</option>
                                <option value="2-3">2:00 PM - 3:00 PM</option>
                                <option value="3-4">3:00 PM - 4:00 PM</option>
                                <option value="4-5">4:00 PM - 5:00 PM</option>
                                <option value="5-6">5:00 PM - 6:00 PM</option>
                            </select>
                        </div>
                        <div class="product-actions">
                            <button class="btn btn-sm btn-primary add-to-cart" data-product="Oil Filter Kit">Add to Cart</button>
                            <button class="btn btn-sm btn-secondary find-in-store" data-product="Oil Filter Kit">Find in Store</button>
                        </div>
                    </div>
                </div>

                <div class="product-card" data-tab="recently-viewed">
                    <div class="product-image">
                        <img src="images/SparkPlugSet.jpg" alt="Spark Plug Set">
                        <button class="wishlist-btn" data-product="Spark Plug Set"><i class="far fa-heart"></i></button>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Ignition System</div>
                        <h3 class="product-name">Iridium Spark Plug Set (4 pcs)</h3>
                        <div class="product-price">₹1,499</div>
                        <div class="product-availability">
                            <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock at 2 Nearby Stores</span>
                        </div>
                        <div class="product-delivery-options">
                            <select class="delivery-option-select">
                                <option value="">Select Pickup/Delivery Option</option>
                                <option value="pickup">Pickup In-Store</option>
                                <option value="delivery">Home Delivery</option>
                            </select>
                            <select class="time-slot-select" style="display: none;">
                                <option value="">Select Pickup Time</option>
                                <option value="10-11">10:00 AM - 11:00 AM</option>
                                <option value="11-12">11:00 AM - 12:00 PM</option>
                                <option value="12-1">12:00 PM - 1:00 PM</option>
                                <option value="1-2">1:00 PM - 2:00 PM</option>
                                <option value="2-3">2:00 PM - 3:00 PM</option>
                                <option value="3-4">3:00 PM - 4:00 PM</option>
                                <option value="4-5">4:00 PM - 5:00 PM</option>
                                <option value="5-6">5:00 PM - 6:00 PM</option>
                            </select>
                        </div>
                        <div class="product-actions">
                            <button class="btn btn-sm btn-primary add-to-cart" data-product="Spark Plug Set">Add to Cart</button>
                            <button class="btn btn-sm btn-secondary find-in-store" data-product="Spark Plug Set">Find in Store</button>
                        </div>
                    </div>
                </div>

                <div class="product-card" data-tab="on-sale">
                    <div class="product-image">
                        <img src="images/WheelLugNuts.jpg" alt="Wheel Lug Nuts">
                        <div class="product-badge">
                            <span class="sale">15% OFF</span>
                        </div>
                        <button class="wishlist-btn" data-product="Wheel Lug Nuts"><i class="far fa-heart"></i></button>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Nuts & Bolts</div>
                        <h3 class="product-name">Wheel Lug Nuts (Set of 20)</h3>
                        <div class="product-price">₹849 <span style="text-decoration: line-through; font-size: 0.8em; color: var(--gray-color);">₹999</span></div>
                        <div class="product-availability">
                            <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock at 4 Nearby Stores</span>
                        </div>
                        <div class="product-delivery-options">
                            <select class="delivery-option-select">
                                <option value="">Select Pickup/Delivery Option</option>
                                <option value="pickup">Pickup In-Store</option>
                                <option value="delivery">Home Delivery</option>
                            </select>
                            <select class="time-slot-select" style="display: none;">
                                <option value="">Select Pickup Time</option>
                                <option value="10-11">10:00 AM - 11:00 AM</option>
                                <option value="11-12">11:00 AM - 12:00 PM</option>
                                <option value="12-1">12:00 PM - 1:00 PM</option>
                                <option value="1-2">1:00 PM - 2:00 PM</option>
                                <option value="2-3">2:00 PM - 3:00 PM</option>
                                <option value="3-4">3:00 PM - 4:00 PM</option>
                                <option value="4-5">4:00 PM - 5:00 PM</option>
                                <option value="5-6">5:00 PM - 6:00 PM</option>
                            </select>
                        </div>
                        <div class="product-actions">
                            <button class="btn btn-sm btn-primary add-to-cart" data-product="Wheel Lug Nuts">Add to Cart</button>
                            <button class="btn btn-sm btn-secondary find-in-store" data-product="Wheel Lug Nuts">Find in Store</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="categories">
        <div class="container">
            <div class="section-header">
                <h2>Browse Categories</h2>
                <p>Find the exact automotive parts you need by category</p>
            </div>

            <div class="category-grid">
                <div class="category-card">
                    <div class="category-image">
                        <img src="images/NutsandBolts.jpg" alt="Nuts and Bolts">
                    </div>
                    <div class="category-info">
                        <h3>Nuts & Bolts</h3>
                        <p>Fasteners for every application</p>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-image">
                        <img src="images/Screws.jpg" alt="Screws">
                    </div>
                    <div class="category-info">
                        <h3>Screws</h3>
                        <p>Machine & sheet metal screws</p>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-image">
                        <img src="images/EngineParts.jpg" alt="Engine Parts">
                    </div>
                    <div class="category-info">
                        <h3>Engine Parts</h3>
                        <p>Gaskets, pistons, valves & more</p>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-image">
                        <img src="images/Bushes.jpg" alt="Bushes">
                    </div>
                    <div class="category-info">
                        <h3>Bushes</h3>
                        <p>Suspension & control arm bushes</p>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-image">
                        <img src="images/Screws.jpg" alt="Screws">
                    </div>
                    <div class="category-info">
                        <h3>Screws</h3>
                        <p>Machine & sheet metal screws</p>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-image">
                        <img src="images/EngineParts.jpg" alt="Engine Parts">
                    </div>
                    <div class="category-info">
                        <h3>Engine Parts</h3>
                        <p>Gaskets, pistons, valves & more</p>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <section class="products">
        <div class="container">
            <div class="section-header">
                <h2>Featured Products</h2>
                <p>Quality aftermarket parts for your vehicle maintenance and repair needs</p>
            </div>

            <div class="products-container">
                <div class="filters">
                    <div class="filter-group">
                        <h3>Part Type</h3>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" name="part-type" value="nuts-bolts" checked> Nuts & Bolts
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="part-type" value="screws" checked> Screws
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="part-type" value="engine-parts" checked> Engine Parts
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="part-type" value="bushes" checked> Bushes
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="part-type" value="starters" checked> Starters
                            </label>
                        </div>
                    </div>

                    <div class="filter-group">
                        <h3>Vehicle Compatibility</h3>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" name="vehicle" value="maruti"> Maruti Suzuki
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="vehicle" value="hyundai"> Hyundai
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="vehicle" value="tata"> Tata
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="vehicle" value="mahindra"> Mahindra
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="vehicle" value="honda"> Honda
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="vehicle" value="toyota"> Toyota
                            </label>
                        </div>
                    </div>

                    <div class="filter-group">
                        <h3>Brand</h3>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" name="brand" value="bosch"> Bosch
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="brand" value="valeo"> Valeo
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="brand" value="denso"> Denso
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="brand" value="minda"> Minda
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="brand" value="lumax"> Lumax
                            </label>
                        </div>
                    </div>

                    <div class="filter-group">
                        <h3>Price Range</h3>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" name="price" value="0-500"> ₹0 - ₹500
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="price" value="500-1000"> ₹500 - ₹1,000
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="price" value="1000-5000"> ₹1,000 - ₹5,000
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="price" value="5000+"> ₹5,000+
                            </label>
                        </div>
                    </div>

                    <div class="filter-group">
                        <h3>Availability</h3>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" name="availability" value="in-stock" checked> In Stock
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="availability" value="out-of-stock"> Out of Stock
                            </label>
                        </div>
                    </div>

                    <button class="btn btn-primary" style="width: 100%;">Apply Filters</button>
                </div>

                <div class="products-grid">
                    <!-- Nuts & Bolts -->
                    <div class="product-card" data-category="nuts-bolts">
                        <div class="product-image">
                            <img src="images/HighTensileHexBolts.jpg" alt="High Tensile Hex Bolts">
                            <div class="product-badge">
                                <span class="new">New</span>
                            </div>
                        </div>
                        <div class="product-info">
                            <div class="product-category">Nuts & Bolts</div>
                            <h3 class="product-name">High Tensile Hex Bolts (M10)</h3>
                            <div class="product-price">₹299</div>
                            <div class="product-availability">
                                <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary">Add to Cart</button>
                                <button class="btn btn-secondary"><i class="far fa-heart"></i></button>
                            </div>
                        </div>
                    </div>

                    <div class="product-card" data-category="nuts-bolts">
                        <div class="product-image">
                            <img src="images/NutsandBolts.jpg" alt="Flange Nuts Assortment">
                        </div>
                        <div class="product-info">
                            <div class="product-category">Nuts & Bolts</div>
                            <h3 class="product-name">Flange Nuts Assortment Kit</h3>
                            <div class="product-price">₹499</div>
                            <div class="product-availability">
                                <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary">Add to Cart</button>
                                <button class="btn btn-secondary"><i class="far fa-heart"></i></button>
                            </div>
                        </div>
                    </div>

                    <div class="product-card" data-category="nuts-bolts">
                        <div class="product-image">
                            <img src="images/WheelLugNuts.jpg" alt="Wheel Lug Nuts">
                            <div class="product-badge">
                                <span class="sale">15% OFF</span>
                            </div>
                        </div>
                        <div class="product-info">
                            <div class="product-category">Nuts & Bolts</div>
                            <h3 class="product-name">Wheel Lug Nuts (Set of 20)</h3>
                            <div class="product-price">₹849 <span style="text-decoration: line-through; font-size: 0.8em; color: var(--gray-color);">₹999</span></div>
                            <div class="product-availability">
                                <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary">Add to Cart</button>
                                <button class="btn btn-secondary"><i class="far fa-heart"></i></button>
                            </div>
                        </div>
                    </div>

                    <!-- Screws -->
                    <div class="product-card" data-category="screws">
                        <div class="product-image">
                            <img src="images/MachineScrewsSet.jpg" alt="Machine Screws Set">
                            <div class="product-badge">
                                <span class="sale">20% OFF</span>
                            </div>
                        </div>
                        <div class="product-info">
                            <div class="product-category">Screws</div>
                            <h3 class="product-name">Machine Screws Set (120 pcs)</h3>
                            <div class="product-price">₹399 <span style="text-decoration: line-through; font-size: 0.8em; color: var(--gray-color);">₹499</span></div>
                            <div class="product-availability">
                                <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary">Add to Cart</button>
                                <button class="btn btn-secondary"><i class="far fa-heart"></i></button>
                            </div>
                        </div>
                    </div>


                    <!-- Bushes -->
                    <div class="product-card" data-category="bushes">
                        <div class="product-image">
                            <img src="images/SuspensionBushesKit.jpg" alt="Suspension Bushes">
                        </div>
                        <div class="product-info">
                            <div class="product-category">Bushes</div>
                            <h3 class="product-name">Suspension Bushes Kit</h3>
                            <div class="product-price">₹1,299</div>
                            <div class="product-availability">
                                <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary">Add to Cart</button>
                                <button class="btn btn-secondary"><i class="far fa-heart"></i></button>
                            </div>
                        </div>
                    </div>



                    <div class="product-card" data-category="bushes">
                        <div class="product-image">
                            <img src="images/barbushes.jpg" alt="Stabilizer Bar Bushes">
                        </div>
                        <div class="product-info">
                            <div class="product-category">Bushes</div>
                            <h3 class="product-name">Stabilizer Bar Bushes (Set of 4)</h3>
                            <div class="product-price">₹599</div>
                            <div class="product-availability">
                                <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary">Add to Cart</button>
                                <button class="btn btn-secondary"><i class="far fa-heart"></i></button>
                            </div>
                        </div>
                    </div>

                    <!-- Starters -->
                    <div class="product-card" data-category="starters">
                        <div class="product-image">
                            <img src="images/StarterMotor.jpg" alt="Starter Motor">
                        </div>
                        <div class="product-info">
                            <div class="product-category">Starters</div>
                            <h3 class="product-name">Starter Motor (Maruti Suzuki)</h3>
                            <div class="product-price">₹4,999</div>
                            <div class="product-availability">
                                <span class="in-stock"><i class="fas fa-check-circle"></i> In Stock</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary">Add to Cart</button>
                                <button class="btn btn-secondary"><i class="far fa-heart"></i></button>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </section>





    <footer>
        <div class="container">
            <div class="footer-container">
                <div class="footer-column">
                    <h3>Shop</h3>
                    <ul>
                        <li><a href="#">Nuts & Bolts</a></li>
                        <li><a href="#">Screws</a></li>
                        <li><a href="#">Engine Parts</a></li>
                        <li><a href="#">Bushes</a></li>
                        <li><a href="#">Starters</a></li>
                        <li><a href="#">All Categories</a></li>
                    </ul>
                </div>

                <div class="footer-column">
                    <h3>Customer Service</h3>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">FAQs</a></li>
                        <li><a href="#">Shipping & Returns</a></li>
                        <li><a href="#">Warranty Information</a></li>
                        <li><a href="#">Product Support</a></li>
                    </ul>
                </div>

                <div class="footer-column">
                    <h3>About Us</h3>
                    <ul>
                        <li><a href="#">Our Story</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Store Locations</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Press</a></li>
                    </ul>
                </div>

                <div class="footer-column">
                    <h3>Connect With Us</h3>
                    <ul>
                        <li><a href="#"><i class="fab fa-facebook"></i> Facebook</a></li>
                        <li><a href="#"><i class="fab fa-twitter"></i> Twitter</a></li>
                        <li><a href="#"><i class="fab fa-instagram"></i> Instagram</a></li>
                        <li><a href="#"><i class="fab fa-youtube"></i> YouTube</a></li>
                        <li><a href="#"><i class="fab fa-linkedin"></i> LinkedIn</a></li>
                    </ul>
                </div>
            </div>

            <div class="copyright">
                <p>&copy; 2025 Auto Parts. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Helper function to check if an element is in the viewport
        function isElementInViewport(el) {
            if (!el) return false;
            const rect = el.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }

        // Theme Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const lightIcon = document.querySelector('.light-icon');
            const darkIcon = document.querySelector('.dark-icon');

            themeToggle.addEventListener('click', function() {
                document.documentElement.setAttribute(
                    'data-theme',
                    document.documentElement.getAttribute('data-theme') === 'light' ? 'dark' : 'light'
                );

                // Toggle icons
                if (lightIcon.style.display === 'none') {
                    lightIcon.style.display = 'inline-block';
                    darkIcon.style.display = 'none';
                } else {
                    lightIcon.style.display = 'none';
                    darkIcon.style.display = 'inline-block';
                }
            });

            // Recommendation Tabs Functionality
            const tabButtons = document.querySelectorAll('.tab-btn');
            const productCards = document.querySelectorAll('#recommendation-products .product-card');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons
                    tabButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    button.classList.add('active');

                    const tabName = button.getAttribute('data-tab');

                    // Show/hide products based on tab
                    productCards.forEach(card => {
                        const dataTabs = card.getAttribute('data-tab');

                        if (tabName === 'all' || dataTabs.includes(tabName)) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Store View Functionality
            const viewStoreButtons = document.querySelectorAll('.view-store');
            const recommendationsSection = document.querySelector('.recommendations');
            const storeProductsContainer = document.createElement('div');
            storeProductsContainer.className = 'store-products-container';
            storeProductsContainer.style.display = 'none';

            // Insert the store products container at the beginning of recommendations section
            const recommendationsContainer = recommendationsSection.querySelector('.container');
            recommendationsContainer.insertBefore(storeProductsContainer, recommendationsContainer.firstChild);

            // Store-specific products data
            window.storeProducts = {
                downtown: [
                    {
                        category: 'Nuts & Bolts',
                        name: 'High Tensile Hex Bolts (M10)',
                        price: '₹299',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: true
                    },
                    {
                        category: 'Engine Parts',
                        name: 'Complete Engine Gasket Set',
                        price: '₹1,999',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: false
                    },
                    {
                        category: 'Bushes',
                        name: 'Suspension Bushes Kit',
                        price: '₹1,299',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: false
                    },
                    {
                        category: 'Starters',
                        name: 'Starter Motor (Maruti Suzuki)',
                        price: '₹4,999',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: false
                    }
                ],
                outlet: [
                    {
                        category: 'Screws',
                        name: 'Machine Screws Set (120 pcs)',
                        price: '₹399',
                        oldPrice: '₹499',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: false,
                        onSale: true
                    },
                    {
                        category: 'Engine Parts',
                        name: 'Piston Rings Set (4 Cylinder)',
                        price: '₹2,499',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: true
                    },
                    {
                        category: 'Bushes',
                        name: 'Control Arm Bushes (Pair)',
                        price: '₹849',
                        oldPrice: '₹999',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: false,
                        onSale: true
                    }
                ],
                partner: [
                    {
                        category: 'Nuts & Bolts',
                        name: 'Wheel Lug Nuts (Set of 20)',
                        price: '₹849',
                        oldPrice: '₹999',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: false,
                        onSale: true
                    },
                    {
                        category: 'Engine Parts',
                        name: 'Fuel Injector (Maruti Suzuki)',
                        price: '₹2,699',
                        oldPrice: '₹2,999',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: false,
                        onSale: true
                    },
                    {
                        category: 'Starters',
                        name: 'Starter Solenoid (Universal)',
                        price: '₹899',
                        image: 'images/NutsandBolts.jpg',
                        inStock: true,
                        isNew: true
                    }
                ]
            };

            // Function to generate product HTML
            function generateProductHTML(product) {
                return `
                    <div class="product-card">
                        <div class="product-image">
                            <img src="${product.image}" alt="${product.name}">
                            ${product.isNew ? '<div class="product-badge"><span class="new">New</span></div>' : ''}
                            ${product.onSale ? '<div class="product-badge"><span class="sale">Sale</span></div>' : ''}
                            <button class="wishlist-btn" data-product="${product.name}"><i class="far fa-heart"></i></button>
                        </div>
                        <div class="product-info">
                            <div class="product-category">${product.category}</div>
                            <h3 class="product-name">${product.name}</h3>
                            <div class="product-price">${product.price} ${product.oldPrice ? `<span style="text-decoration: line-through; font-size: 0.8em; color: var(--gray-color);">${product.oldPrice}</span>` : ''}</div>
                            <div class="product-availability">
                                <span class="${product.inStock ? 'in-stock' : 'out-of-stock'}">
                                    <i class="fas ${product.inStock ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                    ${product.inStock ? 'In Stock' : 'Out of Stock'}
                                </span>
                            </div>
                            <div class="product-delivery-options">
                                <select class="delivery-option-select">
                                    <option value="">Select Pickup/Delivery Option</option>
                                    <option value="pickup">Pickup In-Store</option>
                                    <option value="delivery">Home Delivery</option>
                                </select>
                                <select class="time-slot-select" style="display: none;">
                                    <option value="">Select Pickup Time</option>
                                    <option value="10-11">10:00 AM - 11:00 AM</option>
                                    <option value="11-12">11:00 AM - 12:00 PM</option>
                                    <option value="12-1">12:00 PM - 1:00 PM</option>
                                    <option value="1-2">1:00 PM - 2:00 PM</option>
                                    <option value="2-3">2:00 PM - 3:00 PM</option>
                                    <option value="3-4">3:00 PM - 4:00 PM</option>
                                    <option value="4-5">4:00 PM - 5:00 PM</option>
                                    <option value="5-6">5:00 PM - 6:00 PM</option>
                                </select>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary add-to-cart" data-product="${product.name}">Add to Cart</button>
                                <button class="btn btn-secondary"><i class="far fa-heart"></i></button>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Function to show store products
            function showStoreProducts(storeId) {
                // Get the store name for display
                const storeName = document.querySelector(`#store-${storeId} h3`).textContent;

                // Hide the recommendations section content
                const recommendationsHeader = recommendationsSection.querySelector('.section-header');
                const recommendationTabs = recommendationsSection.querySelector('.recommendation-tabs');
                const recommendationProducts = recommendationsSection.querySelector('.products-grid');

                if (recommendationsHeader && recommendationTabs && recommendationProducts) {
                    recommendationsHeader.style.display = 'none';
                    recommendationTabs.style.display = 'none';
                    recommendationProducts.style.display = 'none';
                }

                // Show the store products container
                storeProductsContainer.style.display = 'block';

                // Generate HTML for store products
                let storeProductsHTML = `
                    <div class="store-products-header">
                        <h2>Products at ${storeName}</h2>
                        <p>Browse available products at this location</p>
                        <button class="btn btn-secondary back-to-recommendations">← Back to Recommendations</button>
                    </div>
                    <div class="products-grid">
                `;

                // Add products for this store
                window.storeProducts[storeId].forEach(product => {
                    storeProductsHTML += generateProductHTML(product);
                });

                storeProductsHTML += '</div>';

                // Set the HTML
                storeProductsContainer.innerHTML = storeProductsHTML;

                // Scroll to the recommendations section (where store products are now shown)
                recommendationsSection.scrollIntoView({ behavior: 'smooth' });

                // Add event listener to the back button
                const backButton = storeProductsContainer.querySelector('.back-to-recommendations');
                if (backButton) {
                    backButton.addEventListener('click', () => {
                        // Hide store products
                        storeProductsContainer.style.display = 'none';

                        // Show recommendations content with proper display values
                        if (recommendationsHeader && recommendationTabs && recommendationProducts) {
                            recommendationsHeader.style.display = '';
                            recommendationTabs.style.display = '';
                            recommendationProducts.style.display = '';
                        }

                        // Ensure the recommendations section is properly visible
                        recommendationsSection.style.display = '';

                        // Scroll back to recommendations section
                        setTimeout(() => {
                            recommendationsSection.scrollIntoView({ behavior: 'smooth' });
                        }, 100);
                    });
                }

                // Add event listeners to new wishlist buttons
                const newWishlistButtons = storeProductsContainer.querySelectorAll('.wishlist-btn');
                newWishlistButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        button.classList.toggle('active');
                        const icon = button.querySelector('i');

                        if (icon.classList.contains('far')) {
                            icon.classList.remove('far');
                            icon.classList.add('fas');
                        } else {
                            icon.classList.remove('fas');
                            icon.classList.add('far');
                        }
                    });
                });

                // Add event listeners to new add to cart buttons
                const newAddToCartButtons = storeProductsContainer.querySelectorAll('.add-to-cart');
                newAddToCartButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const productName = button.getAttribute('data-product');
                        alert(`${productName} added to cart!`);
                    });
                });

                // Add event listeners to delivery option selects
                const newDeliverySelects = storeProductsContainer.querySelectorAll('.delivery-option-select');
                newDeliverySelects.forEach(select => {
                    select.addEventListener('change', () => {
                        const timeSlotSelect = select.parentNode.querySelector('.time-slot-select');
                        if (select.value === 'pickup') {
                            timeSlotSelect.style.display = 'block';
                        } else {
                            timeSlotSelect.style.display = 'none';
                        }
                    });
                });
            }

            // Add click event listeners to view store buttons
            viewStoreButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const storeId = button.getAttribute('data-store');
                    showStoreProducts(storeId);
                });
            });

            // Store Finder Functionality

            // Distance slider
            const distanceSlider = document.getElementById('distance-slider');
            const distanceValue = document.getElementById('distance-value');
            const storeCount = document.getElementById('store-count');

            if (distanceSlider && distanceValue) {
                distanceSlider.addEventListener('input', () => {
                    const value = distanceSlider.value;
                    distanceValue.textContent = `${value} miles`;

                    // Filter stores based on distance
                    const storeCards = document.querySelectorAll('.store-card');
                    let visibleCount = 0;

                    storeCards.forEach(card => {
                        const storeDistance = parseFloat(card.querySelector('.store-distance').textContent.match(/\d+\.\d+/)[0]);
                        if (storeDistance <= value) {
                            card.style.display = 'block';
                            visibleCount++;
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    // Update store count
                    if (storeCount) {
                        storeCount.textContent = `We found ${visibleCount} stores within ${value} miles of your location`;
                    }
                });
            }

            // Filter tabs
            const filterTabs = document.querySelectorAll('.filter-tab');

            filterTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Get parent filter group
                    const filterGroup = tab.closest('.filter-group');

                    // Remove active class from all tabs in this group
                    const tabs = filterGroup.querySelectorAll('.filter-tab');
                    tabs.forEach(t => t.classList.remove('active'));

                    // Add active class to clicked tab
                    tab.classList.add('active');

                    // Get view type
                    const viewType = tab.getAttribute('data-filter-view');

                    // Hide all views in this group
                    const views = filterGroup.querySelectorAll('.filter-view');
                    views.forEach(v => v.classList.remove('active'));

                    // Show selected view
                    const selectedView = filterGroup.querySelector(`.${viewType}-view`);
                    if (selectedView) {
                        selectedView.classList.add('active');
                    }
                });
            });

            // Apply filters button
            const applyFiltersBtn = document.getElementById('apply-filters');

            if (applyFiltersBtn) {
                applyFiltersBtn.addEventListener('click', () => {
                    // Get all checked checkboxes
                    const checkedStoreTypes = Array.from(document.querySelectorAll('input[data-filter="store-type"]:checked')).map(cb => cb.value);
                    const checkedCategories = Array.from(document.querySelectorAll('input[data-filter="category"]:checked')).map(cb => cb.value);
                    const checkedServices = Array.from(document.querySelectorAll('input[data-filter="service"]:checked')).map(cb => cb.value);

                    // Get sort option from dropdown
                    const sortOption = document.getElementById('service-sort').value;

                    // Get distance
                    const distance = document.getElementById('distance-slider').value;

                    // Get search query
                    const searchQuery = document.getElementById('search-input').value.toLowerCase();

                    console.log('Applying filters:');
                    console.log('Store Types:', checkedStoreTypes);
                    console.log('Categories:', checkedCategories);
                    console.log('Services:', checkedServices);
                    console.log('Distance:', distance);
                    console.log('Search Query:', searchQuery);

                    // Filter stores
                    const storeCards = document.querySelectorAll('.store-card');

                    storeCards.forEach(card => {
                        // Check if store matches filters
                        const storeTypeElement = card.querySelector('.store-badge span');
                        const storeType = storeTypeElement.className; // Class name (e.g., "flagship", "outlet")
                        const storeTypeText = storeTypeElement.textContent.trim(); // Text content (e.g., "Flagship Store", "Factory Outlet")
                        const storeServices = Array.from(card.querySelectorAll('.service-tag')).map(tag => tag.textContent.trim());
                        const storeDistance = parseFloat(card.querySelector('.store-distance').textContent.match(/\d+\.\d+/)[0]);
                        console.log(`Store: ${card.querySelector('h3').textContent}, Type Class: ${storeType}, Type Text: ${storeTypeText}`);
                        const storeName = card.querySelector('h3').textContent.toLowerCase();
                        const storeAddress = card.querySelector('.store-address').textContent.toLowerCase();

                        // Check if store matches search query
                        const matchesSearch = searchQuery === '' ||
                                            storeName.includes(searchQuery) ||
                                            storeAddress.includes(searchQuery);

                        // Check if store has products matching the search query
                        const storeId = card.id.replace('store-', '');
                        const storeProductsList = window.storeProducts[storeId] || [];
                        const hasMatchingProduct = storeProductsList.some(product =>
                            product.name.toLowerCase().includes(searchQuery) ||
                            product.category.toLowerCase().includes(searchQuery)
                        );

                        // Update matchesSearch to include product matches
                        const matchesSearchFinal = matchesSearch || (searchQuery !== '' && hasMatchingProduct);

                        // Check if store matches store type filter
                        const matchesStoreType = checkedStoreTypes.length === 0 ||
                                                checkedStoreTypes.some(type => {
                                                    // Debug store type matching
                                                    console.log(`Checking if store type "${storeTypeText}" matches filter "${type}"`);

                                                    // Match based on the text content of the store badge, not the class name
                                                    return storeTypeText.toLowerCase() === type.toLowerCase();
                                                });

                        // Check if store matches service filter
                        const matchesService = checkedServices.length === 0 ||
                                            checkedServices.some(service => {
                                                // Convert service to lowercase for case-insensitive comparison
                                                const serviceLower = service.toLowerCase();
                                                return storeServices.some(s => s.toLowerCase().includes(serviceLower));
                                            });

                        // Check if store is within distance
                        const matchesDistance = storeDistance <= distance;

                        // Show/hide store based on filters
                        if (matchesSearchFinal && matchesStoreType && matchesService && matchesDistance) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    // Update store count
                    const visibleStores = document.querySelectorAll('.store-card[style="display: block"]').length;
                    if (storeCount) {
                        storeCount.textContent = `We found ${visibleStores} stores within ${distance} miles of your location`;
                    }

                    // Sort stores
                    const storesGrid = document.querySelector('.stores-grid');
                    const storeCardsArray = Array.from(storeCards).filter(card => card.style.display !== 'none');

                    if (sortOption === 'distance') {
                        storeCardsArray.sort((a, b) => {
                            const distanceA = parseFloat(a.querySelector('.store-distance').textContent.match(/\d+\.\d+/)[0]);
                            const distanceB = parseFloat(b.querySelector('.store-distance').textContent.match(/\d+\.\d+/)[0]);
                            return distanceA - distanceB;
                        });
                    } else if (sortOption === 'rating') {
                        storeCardsArray.sort((a, b) => {
                            const ratingA = parseFloat(a.querySelector('.store-rating span:last-child').textContent.match(/\d+\.\d+/)[0]);
                            const ratingB = parseFloat(b.querySelector('.store-rating span:last-child').textContent.match(/\d+\.\d+/)[0]);
                            return ratingB - ratingA;
                        });
                    } else if (sortOption === 'name-asc') {
                        storeCardsArray.sort((a, b) => {
                            const nameA = a.querySelector('h3').textContent;
                            const nameB = b.querySelector('h3').textContent;
                            return nameA.localeCompare(nameB);
                        });
                    } else if (sortOption === 'name-desc') {
                        storeCardsArray.sort((a, b) => {
                            const nameA = a.querySelector('h3').textContent;
                            const nameB = b.querySelector('h3').textContent;
                            return nameB.localeCompare(nameA);
                        });
                    }

                    // Reorder stores in the DOM
                    storeCardsArray.forEach(card => {
                        storesGrid.appendChild(card);
                    });
                });
            }

            // Barcode scanner button
            const scanBarcodeBtn = document.getElementById('scan-barcode');

            if (scanBarcodeBtn) {
                scanBarcodeBtn.addEventListener('click', () => {
                    alert('Barcode scanner functionality would be implemented here. This would allow users to scan product barcodes or upload images to find stores with specific products.');
                });
            }

            // Add direct search functionality to store finder search input
            const storeFinderSearch = document.getElementById('search-input');
            const clearSearchBtn = document.getElementById('clear-search');
            const searchButton = document.getElementById('search-button');

            if (storeFinderSearch) {
                // Add input event listener to handle empty search box and show/hide clear button
                storeFinderSearch.addEventListener('input', () => {
                    const searchTerm = storeFinderSearch.value.toLowerCase();

                    // Show/hide clear button based on input content
                    if (searchTerm.trim() !== '') {
                        clearSearchBtn.style.display = 'block';
                    } else {
                        clearSearchBtn.style.display = 'none';
                    }

                    // If search term is empty, show all stores
                    if (searchTerm.trim() === '') {
                        console.log('Search box is empty, showing all stores');
                        const storeCards = document.querySelectorAll('.store-card');

                        storeCards.forEach(card => {
                            card.style.display = 'block';
                        });

                        // Update store count for all stores
                        const storeCount = document.getElementById('store-count');
                        if (storeCount) {
                            const totalStores = storeCards.length;
                            const distance = document.getElementById('distance-slider').value;
                            storeCount.textContent = `We found ${totalStores} stores within ${distance} miles of your location`;
                        }

                        // Apply other filters
                        document.getElementById('apply-filters').click();
                    }
                });

                // Add click event for clear button
                if (clearSearchBtn) {
                    clearSearchBtn.addEventListener('click', () => {
                        storeFinderSearch.value = '';
                        clearSearchBtn.style.display = 'none';

                        // Show all stores
                        const storeCards = document.querySelectorAll('.store-card');
                        storeCards.forEach(card => {
                            card.style.display = 'block';
                        });

                        // Update store count for all stores
                        const storeCount = document.getElementById('store-count');
                        if (storeCount) {
                            const totalStores = storeCards.length;
                            const distance = document.getElementById('distance-slider').value;
                            storeCount.textContent = `We found ${totalStores} stores within ${distance} miles of your location`;
                        }

                        // Apply other filters
                        document.getElementById('apply-filters').click();
                    });
                }

                // Add click event for search button
                if (searchButton) {
                    searchButton.addEventListener('click', () => {
                        // Trigger the Enter key press event
                        const event = new KeyboardEvent('keypress', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true
                        });
                        storeFinderSearch.dispatchEvent(event);
                    });
                }

                // Handle Enter key press for search
                storeFinderSearch.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        const searchTerm = storeFinderSearch.value.toLowerCase();
                        const storeCards = document.querySelectorAll('.store-card');

                        // If search term is empty, show all stores
                        if (searchTerm.trim() === '') {
                            storeCards.forEach(card => {
                                card.style.display = 'block';
                            });

                            // Update store count for all stores
                            const storeCount = document.getElementById('store-count');
                            if (storeCount) {
                                const totalStores = storeCards.length;
                                const distance = document.getElementById('distance-slider').value;
                                storeCount.textContent = `We found ${totalStores} stores within ${distance} miles of your location`;
                            }

                            // Apply other filters
                            document.getElementById('apply-filters').click();
                            return;
                        }

                        // Filter stores based on product search
                        let foundStores = 0;

                        storeCards.forEach(card => {
                            const storeId = card.id.replace('store-', '');
                            const storeProducts = window.storeProducts[storeId] || [];

                            // Check if any product in this store matches the search term
                            const hasMatchingProduct = storeProducts.some(product =>
                                product.name.toLowerCase().includes(searchTerm) ||
                                product.category.toLowerCase().includes(searchTerm)
                            );

                            // Also check store name and address
                            const storeName = card.querySelector('h3').textContent.toLowerCase();
                            const storeAddress = card.querySelector('.store-address').textContent.toLowerCase();
                            const matchesStore = storeName.includes(searchTerm) || storeAddress.includes(searchTerm);

                            if (hasMatchingProduct || matchesStore) {
                                card.style.display = 'block';
                                foundStores++;
                            } else {
                                card.style.display = 'none';
                            }
                        });

                        // Update store count
                        const storeCount = document.getElementById('store-count');
                        if (storeCount) {
                            storeCount.textContent = `We found ${foundStores} stores with "${searchTerm}" in stock or matching your search`;
                        }

                        // Show a message if no stores found
                        if (foundStores === 0) {
                            alert(`No stores found with "${searchTerm}" in stock. Please try a different search term.`);
                        }
                    }
                });
            }

            // Add store data for the new Express store
            window.storeProducts.express = [
                {
                    category: 'Nuts & Bolts',
                    name: 'Flange Nuts Assortment Kit',
                    price: '₹499',
                    image: 'images/FlangeNutsAssortmentKit.jpg',
                    inStock: true,
                    isNew: false
                },
                {
                    category: 'Screws',
                    name: 'Sheet Metal Screws Assortment',
                    price: '₹349',
                    image: 'images/SheetMetalScrewsAssortment.jpg',
                    inStock: true,
                    isNew: false
                },
                {
                    category: 'Engine Parts',
                    name: 'Complete Timing Belt Kit',
                    price: '₹3,499',
                    image: 'images/engine-parts.jpg',
                    inStock: true,
                    isNew: true
                },
                {
                    category: 'Bushes',
                    name: 'Stabilizer Bar Bushes (Set of 4)',
                    price: '₹599',
                    image: 'images/bushes.jpg',
                    inStock: true,
                    isNew: false
                }
            ];

            // Handle delivery option selection
            const deliveryOptionSelects = document.querySelectorAll('.delivery-option-select');
            const timeSlotSelects = document.querySelectorAll('.time-slot-select');

            deliveryOptionSelects.forEach((select, index) => {
                select.addEventListener('change', () => {
                    const timeSlotSelect = timeSlotSelects[index];
                    if (select.value === 'pickup') {
                        timeSlotSelect.style.display = 'block';
                    } else {
                        timeSlotSelect.style.display = 'none';
                    }
                });
            });

            // Add delivery options to product cards in the main products section
            const featuredProductCards = document.querySelectorAll('.products-grid:not(#recommendation-products) .product-card');

            featuredProductCards.forEach(card => {
                const productActions = card.querySelector('.product-actions');
                if (productActions) {
                    // Create delivery options container
                    const deliveryOptionsDiv = document.createElement('div');
                    deliveryOptionsDiv.className = 'product-delivery-options';

                    // Create delivery option select
                    const deliverySelect = document.createElement('select');
                    deliverySelect.className = 'delivery-option-select';
                    deliverySelect.innerHTML = `
                        <option value="">Select Pickup/Delivery Option</option>
                        <option value="pickup">Pickup In-Store</option>
                        <option value="delivery">Home Delivery</option>
                    `;

                    // Create time slot select
                    const timeSlotSelect = document.createElement('select');
                    timeSlotSelect.className = 'time-slot-select';
                    timeSlotSelect.style.display = 'none';
                    timeSlotSelect.innerHTML = `
                        <option value="">Select Pickup Time</option>
                        <option value="10-11">10:00 AM - 11:00 AM</option>
                        <option value="11-12">11:00 AM - 12:00 PM</option>
                        <option value="12-1">12:00 PM - 1:00 PM</option>
                        <option value="1-2">1:00 PM - 2:00 PM</option>
                        <option value="2-3">2:00 PM - 3:00 PM</option>
                        <option value="3-4">3:00 PM - 4:00 PM</option>
                        <option value="4-5">4:00 PM - 5:00 PM</option>
                        <option value="5-6">5:00 PM - 6:00 PM</option>
                    `;

                    // Add event listener to show/hide time slot select
                    deliverySelect.addEventListener('change', () => {
                        if (deliverySelect.value === 'pickup') {
                            timeSlotSelect.style.display = 'block';
                        } else {
                            timeSlotSelect.style.display = 'none';
                        }
                    });

                    // Add selects to delivery options div
                    deliveryOptionsDiv.appendChild(deliverySelect);
                    deliveryOptionsDiv.appendChild(timeSlotSelect);

                    // Insert delivery options before product actions
                    productActions.parentNode.insertBefore(deliveryOptionsDiv, productActions);
                }
            });

            // Add search functionality to header search
            const headerSearchInput = document.getElementById('header-search');
            if (headerSearchInput) {
                // Add input event listener to handle empty search box
                headerSearchInput.addEventListener('input', () => {
                    const searchTerm = headerSearchInput.value.toLowerCase();

                    // If search term is empty and we're already in the store finder section
                    if (searchTerm.trim() === '' && isElementInViewport(document.querySelector('.store-finder'))) {
                        console.log('Header search box is empty, showing all stores');
                        const storeCards = document.querySelectorAll('.store-card');

                        storeCards.forEach(card => {
                            card.style.display = 'block';
                        });

                        // Update store count for all stores
                        const storeCount = document.getElementById('store-count');
                        if (storeCount) {
                            const totalStores = storeCards.length;
                            const distance = document.getElementById('distance-slider').value;
                            storeCount.textContent = `We found ${totalStores} stores within ${distance} miles of your location`;
                        }

                        // Update the store finder search input
                        const storeFinderSearch = document.getElementById('search-input');
                        if (storeFinderSearch) {
                            storeFinderSearch.value = '';
                        }

                        // Apply other filters
                        document.getElementById('apply-filters').click();
                    }
                });

                // Handle Enter key press for search
                headerSearchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        const searchTerm = headerSearchInput.value.toLowerCase();

                        // Scroll to store finder section
                        document.querySelector('.store-finder').scrollIntoView({ behavior: 'smooth' });

                        // Set the search input in the store finder
                        const storeFinderSearch = document.getElementById('search-input');
                        if (storeFinderSearch) {
                            storeFinderSearch.value = searchTerm;

                            const storeCards = document.querySelectorAll('.store-card');

                            // If search term is empty, show all stores
                            if (searchTerm.trim() === '') {
                                storeCards.forEach(card => {
                                    card.style.display = 'block';
                                });

                                // Update store count for all stores
                                const storeCount = document.getElementById('store-count');
                                if (storeCount) {
                                    const totalStores = storeCards.length;
                                    const distance = document.getElementById('distance-slider').value;
                                    storeCount.textContent = `We found ${totalStores} stores within ${distance} miles of your location`;
                                }
                                return;
                            }

                            // Filter stores based on product search
                            let foundStores = 0;

                            storeCards.forEach(card => {
                                const storeId = card.id.replace('store-', '');
                                const storeProducts = window.storeProducts[storeId] || [];

                                // Check if any product in this store matches the search term
                                const hasMatchingProduct = storeProducts.some(product =>
                                    product.name.toLowerCase().includes(searchTerm) ||
                                    product.category.toLowerCase().includes(searchTerm)
                                );

                                // Also check store name and address
                                const storeName = card.querySelector('h3').textContent.toLowerCase();
                                const storeAddress = card.querySelector('.store-address').textContent.toLowerCase();
                                const matchesStore = storeName.includes(searchTerm) || storeAddress.includes(searchTerm);

                                if (hasMatchingProduct || matchesStore) {
                                    card.style.display = 'block';
                                    foundStores++;
                                } else {
                                    card.style.display = 'none';
                                }
                            });

                            // Update store count
                            const storeCount = document.getElementById('store-count');
                            if (storeCount) {
                                storeCount.textContent = `We found ${foundStores} stores with "${searchTerm}" in stock or matching your search`;
                            }

                            // Show a message if no stores found
                            if (foundStores === 0) {
                                alert(`No stores found with "${searchTerm}" in stock. Please try a different search term.`);
                            }
                        }
                    }
                });
            }

            // Wishlist Functionality
            const wishlistButtons = document.querySelectorAll('.wishlist-btn');
            const storeFavoriteButtons = document.querySelectorAll('.store-favorite');
            const wishlistPopup = document.getElementById('wishlist-popup');
            const wishlistPopupClose = document.getElementById('wishlist-popup-close');
            const wishlistCancel = document.getElementById('wishlist-cancel');
            const wishlistConfirm = document.getElementById('wishlist-confirm');
            const newGroupInput = document.getElementById('new-group-input');
            let currentStoreElement = null;

            // Handle product wishlist buttons (existing functionality)
            wishlistButtons.forEach(button => {
                button.addEventListener('click', () => {
                    button.classList.toggle('active');
                    const icon = button.querySelector('i');

                    if (icon.classList.contains('far')) {
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                    } else {
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                    }
                });
            });

            // Handle store favorite buttons (show popup)
            storeFavoriteButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    currentStoreElement = button;
                    showWishlistPopup();
                });
            });

            // Show wishlist popup
            function showWishlistPopup() {
                wishlistPopup.classList.add('show');
                // Reset form
                const radioButtons = document.querySelectorAll('input[name="wishlist-group"]');
                radioButtons.forEach(radio => radio.checked = false);
                newGroupInput.style.display = 'none';
                newGroupInput.value = '';
            }

            // Hide wishlist popup
            function hideWishlistPopup() {
                wishlistPopup.classList.remove('show');
                currentStoreElement = null;
            }

            // Close popup events
            wishlistPopupClose.addEventListener('click', hideWishlistPopup);
            wishlistCancel.addEventListener('click', hideWishlistPopup);

            // Close popup when clicking outside
            wishlistPopup.addEventListener('click', (e) => {
                if (e.target === wishlistPopup) {
                    hideWishlistPopup();
                }
            });

            // Handle radio button selection
            const wishlistGroupOptions = document.querySelectorAll('.wishlist-group-option');
            wishlistGroupOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const radio = option.querySelector('input[type="radio"]');
                    radio.checked = true;

                    // Show/hide custom group input
                    if (radio.value === 'custom') {
                        newGroupInput.style.display = 'block';
                        newGroupInput.focus();
                    } else {
                        newGroupInput.style.display = 'none';
                    }

                    // Update visual selection
                    wishlistGroupOptions.forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                });
            });

            // Handle wishlist confirmation
            wishlistConfirm.addEventListener('click', () => {
                const selectedGroup = document.querySelector('input[name="wishlist-group"]:checked');

                if (!selectedGroup) {
                    alert('Please select a wishlist group.');
                    return;
                }

                let groupName = '';
                if (selectedGroup.value === 'custom') {
                    groupName = newGroupInput.value.trim();
                    if (!groupName) {
                        alert('Please enter a name for the new group.');
                        newGroupInput.focus();
                        return;
                    }
                } else {
                    const groupLabels = {
                        'favorites': 'My Favorites',
                        'nearby': 'Nearby Stores',
                        'frequent': 'Frequent Visits',
                        'deals': 'Best Deals'
                    };
                    groupName = groupLabels[selectedGroup.value];
                }

                // Get store name
                const storeCard = currentStoreElement.closest('.store-card');
                const storeName = storeCard.querySelector('h3').textContent;

                // Update the favorite button to active state
                currentStoreElement.classList.add('active');
                const icon = currentStoreElement.querySelector('i');
                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                }

                // Show success message
                alert(`"${storeName}" has been added to "${groupName}" wishlist!`);

                // Hide popup
                hideWishlistPopup();
            });

            // Add to Cart Functionality
            const addToCartButtons = document.querySelectorAll('.add-to-cart');

            addToCartButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const productName = button.getAttribute('data-product');
                    alert(`${productName} added to cart!`);
                });
            });

            // Find in Store Functionality
            const findInStoreButtons = document.querySelectorAll('.find-in-store');

            findInStoreButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const productName = button.getAttribute('data-product');
                    alert(`Finding ${productName} in nearby stores...`);
                });
            });
        });
    </script>

    <!-- Wishlist Popup -->
    <div class="wishlist-popup" id="wishlist-popup">
        <div class="wishlist-popup-content">
            <div class="wishlist-popup-header">
                <h3>Add to Wishlist</h3>
                <button class="wishlist-popup-close" id="wishlist-popup-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="wishlist-groups">
                <label class="wishlist-group-option">
                    <input type="radio" name="wishlist-group" value="favorites">
                    <div class="wishlist-group-info">
                        <div class="wishlist-group-name">
                            <i class="fas fa-heart" style="color: #e74c3c; margin-right: 5px;"></i>
                            My Favorites
                        </div>
                        <div class="wishlist-group-description">Your most loved stores</div>
                    </div>
                </label>

                <label class="wishlist-group-option">
                    <input type="radio" name="wishlist-group" value="nearby">
                    <div class="wishlist-group-info">
                        <div class="wishlist-group-name">
                            <i class="fas fa-map-marker-alt" style="color: #3498db; margin-right: 5px;"></i>
                            Nearby Stores
                        </div>
                        <div class="wishlist-group-description">Convenient locations near you</div>
                    </div>
                </label>

                <label class="wishlist-group-option">
                    <input type="radio" name="wishlist-group" value="frequent">
                    <div class="wishlist-group-info">
                        <div class="wishlist-group-name">
                            <i class="fas fa-shopping-bag" style="color: #f39c12; margin-right: 5px;"></i>
                            Frequent Visits
                        </div>
                        <div class="wishlist-group-description">Stores you visit regularly</div>
                    </div>
                </label>

                <label class="wishlist-group-option">
                    <input type="radio" name="wishlist-group" value="deals">
                    <div class="wishlist-group-info">
                        <div class="wishlist-group-name">
                            <i class="fas fa-tags" style="color: #e67e22; margin-right: 5px;"></i>
                            Best Deals
                        </div>
                        <div class="wishlist-group-description">Stores with great offers</div>
                    </div>
                </label>

                <label class="wishlist-group-option">
                    <input type="radio" name="wishlist-group" value="custom">
                    <div class="wishlist-group-info">
                        <div class="wishlist-group-name">
                            <i class="fas fa-plus" style="color: #27ae60; margin-right: 5px;"></i>
                            Create New Group
                        </div>
                        <div class="wishlist-group-description">Create a custom wishlist group</div>
                    </div>
                </label>
            </div>

            <input type="text" class="new-group-input" id="new-group-input" placeholder="Enter new group name..." style="display: none;">

            <div class="wishlist-popup-actions">
                <button class="btn btn-secondary" id="wishlist-cancel">Cancel</button>
                <button class="btn btn-primary" id="wishlist-confirm">Add to Wishlist</button>
            </div>
        </div>
    </div>

</body>
</html>